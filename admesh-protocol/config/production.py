"""
Production environment configuration
"""
import os
from typing import Dict, Any
from .base import BaseConfig


class ProductionConfig(BaseConfig):
    """Configuration for production environment"""

    @property
    def firebase_config(self) -> Dict[str, Any]:
        """Firebase configuration for production"""
        return {
            "project_id": "admesh-dev",  # Using existing project for simplicity
            "api_key": "AIzaSyDxBNmjuoMjkS5u8iad6PSB_5Lm7ggIkfY",
            "auth_domain": "admesh-dev.firebaseapp.com",
            "storage_bucket": "admesh-dev.firebasestorage.app",
            "messaging_sender_id": "651813374456",
            "app_id": "1:651813374456:web:dfc618425534b042576e0d"
        }

    @property
    def api_base_url(self) -> str:
        """API base URL for production"""
        return os.getenv("API_BASE_URL", "https://api.useadmesh.com")

    @property
    def frontend_url(self) -> str:
        """Frontend URL for production"""
        return os.getenv("FRONTEND_URL", "https://www.useadmesh.com")

    @property
    def cors_origins(self) -> list:
        """CORS origins for production"""
        return [
            "https://www.useadmesh.com",
            "https://useadmesh.com",
            "https://admesh-prod.web.app",
            "https://admesh-prod.firebaseapp.com"
        ]

    @property
    def database_config(self) -> Dict[str, Any]:
        """Database configuration for production"""
        return {
            "project_id": "admesh-prod",
            "database_id": "(default)",
            "emulator_host": None,
            "use_emulator": False
        }

    @property
    def debug(self) -> bool:
        """Debug mode is disabled in production"""
        return False

    @property
    def log_level(self) -> str:
        """Logging level for production"""
        return os.getenv("LOG_LEVEL", "WARNING").upper()

    def get_required_env_vars(self) -> list:
        """Required environment variables for production"""
        return [
            "GOOGLE_APPLICATION_CREDENTIALS",
            "OPENAI_API_KEY",
            "RESEND_API_KEY"
            # Stripe keys are optional for basic functionality
        ]

    @property
    def external_services(self) -> Dict[str, Any]:
        """External service configurations for production"""
        return {
            "openai": {
                "api_key": os.getenv("OPENAI_API_KEY"),
                "base_url": os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1"),
                "model": os.getenv("OPENAI_MODEL", "gpt-4")
            },
            "stripe": {
                "api_key": os.getenv("STRIPE_SECRET_KEY"),
                "publishable_key": os.getenv("STRIPE_PUBLISHABLE_KEY"),
                "webhook_secret": os.getenv("STRIPE_WEBHOOK_SECRET"),
                "use_test_mode": False
            },
            "resend": {
                "api_key": os.getenv("RESEND_API_KEY"),
                "from_email": os.getenv("RESEND_FROM_EMAIL", "<EMAIL>")
            }
        }

    @property
    def feature_flags(self) -> Dict[str, bool]:
        """Feature flags for production"""
        return {
            "enable_analytics": True,
            "enable_rate_limiting": True,
            "enable_caching": True,
            "enable_email_verification": True,
            "enable_trust_score_throttling": True
        }

    @property
    def security_config(self) -> Dict[str, Any]:
        """Security configuration for production"""
        return {
            "allowed_hosts": [
                "api.useadmesh.com",
                "www.useadmesh.com",
                "useadmesh.com"
            ],
            "ssl_required": True,
            "secure_cookies": True,
            "csrf_protection": True,
            "hsts_max_age": 31536000,  # 1 year
            "content_security_policy": {
                "default-src": "'self'",
                "script-src": "'self' 'unsafe-inline' https://www.googletagmanager.com",
                "style-src": "'self' 'unsafe-inline'",
                "img-src": "'self' data: https:",
                "connect-src": "'self' https://api.useadmesh.com"
            }
        }

    @property
    def performance_config(self) -> Dict[str, Any]:
        """Performance configuration for production"""
        return {
            "cache_ttl": 3600,  # 1 hour
            "rate_limit_per_minute": 1000,
            "max_request_size": 10 * 1024 * 1024,  # 10MB
            "timeout_seconds": 30,
            "max_connections": 100
        }
