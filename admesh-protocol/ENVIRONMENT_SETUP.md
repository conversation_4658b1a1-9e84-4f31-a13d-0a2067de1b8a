# AdMesh Protocol Environment Configuration Setup

This document provides a comprehensive guide for setting up environment-based configuration for the AdMesh Protocol codebase.

## ✅ Implementation Summary

The environment-based configuration management system has been successfully implemented with the following components:

### 🏗️ Backend Configuration (Python)
- **Location**: `admesh-protocol/config/`
- **Components**:
  - `base.py` - Base configuration class with common functionality
  - `development.py` - Development environment settings
  - `staging.py` - Staging environment settings
  - `production.py` - Production environment settings
  - `config_manager.py` - Configuration manager with singleton pattern

### 🌐 Frontend Configuration (TypeScript)
- **Location**: `admesh-dashboard/src/config/environment.ts`
- **Features**:
  - Automatic environment detection (Vercel, NODE_ENV, explicit setting)
  - Environment-specific Firebase configurations
  - Dynamic API endpoint configuration

### 📦 SDK Configuration
- **Python SDK**: Updated to use environment-based URL selection
- **TypeScript SDK**: Enhanced with environment detection and URL mapping

### 🚀 Deployment Configuration
- **GitHub Actions**: Updated workflows with environment-specific secrets
- **Docker**: Enhanced Dockerfile with proper environment handling
- **Cloud Run**: Environment variables properly configured for each environment

## 🔧 Key Features Implemented

### 1. Automatic Environment Detection
```python
# Detects environment based on:
# 1. ENV or ENVIRONMENT environment variable
# 2. Cloud Run detection (K_SERVICE)
# 3. Default to development for local environments
```

### 2. Environment-Specific Firebase Projects
- **Development**: `admesh-dev`
- **Production**: `admesh-dev` (same project for simplicity)

### 3. Dynamic URL Configuration
- **Development**: `http://localhost:8080`
- **Production**: `https://api.useadmesh.com`

### 4. Secure Credential Management
- Environment-specific API keys and secrets
- Proper credential file handling for different deployment contexts
- No hardcoded credentials in source code

### 5. Configuration Validation
- Built-in validation for required environment variables
- Validation scripts for testing configuration
- Graceful error handling and fallbacks

## 📋 Environment Variables Required

### Development
```bash
ENV=development
# Optional overrides:
# API_BASE_URL=http://localhost:8080
# DEBUG=true
# LOG_LEVEL=DEBUG
```

### Staging
```bash
ENV=staging
FIREBASE_API_KEY_STAGING=your_staging_api_key
FIREBASE_MESSAGING_SENDER_ID_STAGING=your_staging_sender_id
FIREBASE_APP_ID_STAGING=your_staging_app_id
GOOGLE_APPLICATION_CREDENTIALS=firebase/serviceAccountKey.json
OPENAI_API_KEY=your_openai_key
STRIPE_SECRET_KEY_STAGING=sk_test_...
STRIPE_WEBHOOK_SECRET_STAGING=whsec_...
RESEND_API_KEY=your_resend_key
```

### Production
```bash
ENV=production
FIREBASE_API_KEY_PROD=your_production_api_key
FIREBASE_MESSAGING_SENDER_ID_PROD=your_production_sender_id
FIREBASE_APP_ID_PROD=your_production_app_id
GOOGLE_APPLICATION_CREDENTIALS=firebase/serviceAccountKey.json
OPENAI_API_KEY=your_openai_key
STRIPE_SECRET_KEY_PROD=sk_live_...
STRIPE_WEBHOOK_SECRET_PROD=whsec_...
RESEND_API_KEY=your_resend_key
```

## 🧪 Testing and Validation

### Run Configuration Tests
```bash
cd admesh-protocol
python test_config_simple.py
```

### Validate Environment Setup
```bash
cd admesh-protocol
python scripts/validate_environment.py
```

### Test Environment Configuration
```bash
cd admesh-protocol
python scripts/test_environment_config.py
```

## 📁 File Structure

```
admesh-protocol/
├── config/
│   ├── __init__.py
│   ├── base.py                 # Base configuration class
│   ├── development.py          # Development settings
│   ├── staging.py             # Staging settings
│   ├── production.py          # Production settings
│   ├── config_manager.py      # Configuration manager
│   └── README.md              # Configuration documentation
├── firebase/
│   └── config.py              # Updated Firebase initialization
├── api/
│   └── main.py                # Updated main app with config
├── scripts/
│   ├── validate_environment.py    # Environment validation
│   └── test_environment_config.py # Configuration tests
├── .env.example               # Environment variables template
└── ENVIRONMENT_SETUP.md       # This file

admesh-dashboard/
├── src/
│   ├── config/
│   │   └── environment.ts     # Frontend environment config
│   └── lib/
│       └── firebase.ts        # Updated Firebase config
└── .env.example               # Frontend environment template

.github/workflows/
├── deploy-staging.yml         # Updated staging deployment
└── deploy-production.yml      # Updated production deployment
```

## 🔄 Usage Examples

### Backend Configuration
```python
from config.config_manager import get_config, get_environment

# Get current configuration
config = get_config()
print(f"Environment: {config.environment}")
print(f"API URL: {config.api_base_url}")
print(f"Firebase Project: {config.firebase_config['project_id']}")

# Check environment
if get_environment() == 'production':
    # Production-specific logic
    pass
```

### Frontend Configuration
```typescript
import { config, getCurrentEnvironment } from '@/config/environment';

// Use environment-specific settings
console.log(`Environment: ${config.environment}`);
console.log(`API URL: ${config.api.baseUrl}`);
console.log(`Firebase Project: ${config.firebase.projectId}`);

// Environment checks
if (getCurrentEnvironment() === 'production') {
    // Production-specific logic
}
```

### SDK Configuration
```python
# Python SDK - automatic environment detection
import os
os.environ['ADMESH_ENVIRONMENT'] = 'staging'
from admesh import Admesh

client = Admesh(api_key='your_api_key')
# Automatically uses https://api-staging.useadmesh.com
```

```typescript
// TypeScript SDK - automatic environment detection
process.env.ADMESH_ENVIRONMENT = 'staging';
import { Admesh } from 'admesh';

const client = new Admesh({ apiKey: 'your_api_key' });
// Automatically uses https://api-staging.useadmesh.com
```

## 🔒 Security Considerations

1. **Environment Isolation**: Each environment uses separate Firebase projects and credentials
2. **Secure Defaults**: Production environment has strict security settings
3. **Credential Management**: No hardcoded credentials, all via environment variables
4. **CORS Configuration**: Environment-specific CORS origins for security
5. **Validation**: All configurations are validated before use

## 🚀 Deployment Process

### Staging Deployment
1. Push to `staging` branch
2. GitHub Actions automatically deploys with staging configuration
3. Uses `admesh-staging` Firebase project
4. Environment variables set from GitHub secrets

### Production Deployment
1. Push to `main` branch
2. GitHub Actions automatically deploys with production configuration
3. Uses `admesh-prod` Firebase project
4. Production environment variables and security settings

## 📚 Documentation

- **Configuration Guide**: `docs/environment-configuration.md`
- **Configuration README**: `config/README.md`
- **API Documentation**: Updated with environment-specific examples
- **SDK Documentation**: Updated with environment configuration options

## ✅ Next Steps

1. **Set up Firebase Projects**: Create `admesh-staging` and `admesh-prod` projects
2. **Configure GitHub Secrets**: Add all required environment-specific secrets
3. **Test Deployments**: Deploy to staging and production environments
4. **Update Documentation**: Add environment-specific setup guides
5. **Monitor Logs**: Verify configuration loading in deployed environments

## 🐛 Troubleshooting

### Common Issues
1. **Missing Environment Variables**: Run validation script to identify missing vars
2. **Firebase Connection Issues**: Check project ID and credentials path
3. **CORS Errors**: Verify frontend URL is in CORS origins for the environment
4. **Import Errors**: Ensure config directory is in Python path

### Debug Commands
```bash
# Check configuration loading
python -c "from config.config_manager import get_config; print(get_config().get_all_config())"

# Test Firebase connection
python -c "from firebase.config import initialize_firebase; initialize_firebase()"

# Validate environment
python scripts/validate_environment.py
```

This implementation provides a robust, secure, and scalable environment configuration system for the AdMesh Protocol codebase.
