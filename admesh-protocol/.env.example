# AdMesh Protocol Environment Variables
# Copy this file to .env for local development

# Environment (development, staging, production)
ENV=development

# Firebase Credentials (file path for development)
GOOGLE_APPLICATION_CREDENTIALS=firebase/dev-serviceAccountKey.json

# API Configuration
PORT=8080
DEBUG=true
LOG_LEVEL=DEBUG

# External Services
OPENAI_API_KEY=your_openai_api_key_here
RESEND_API_KEY=your_resend_api_key_here

# Stripe (Development - use test keys)
STRIPE_SECRET_KEY=sk_test_your_stripe_test_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here

# Feature Flags (Development)
ENABLE_ANALYTICS=false
ENABLE_RATE_LIMITING=false
ENABLE_CACHING=false
ENABLE_EMAIL_VERIFICATION=false
ENABLE_TRUST_SCORE_THROTTLING=false

# Optional: Override default URLs
# API_BASE_URL=http://localhost:8080
# FRONTEND_URL=http://localhost:3000

# Optional: Firestore Emulator
# USE_FIRESTORE_EMULATOR=true
# FIRESTORE_EMULATOR_HOST=localhost:8080
