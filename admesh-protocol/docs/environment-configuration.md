# Environment Configuration Guide

This guide explains how to configure AdMesh Protocol for different environments (development, staging, production).

## Overview

AdMesh Protocol uses a comprehensive environment-based configuration system that automatically detects the deployment environment and loads appropriate settings for:

- Firebase configuration (project IDs, API keys)
- API endpoints and URLs
- Database connections
- External service credentials
- Security settings
- Feature flags

## Environment Detection

The system automatically detects the environment using the following priority:

1. `ENV` or `ENVIRONMENT` environment variable
2. Cloud Run detection (`K_SERVICE` environment variable)
3. Default to `development` for local environments

## Supported Environments

### Development
- **Firebase Project**: `admesh-dev`
- **API URL**: `http://localhost:8080`
- **Frontend URL**: `http://localhost:3000`
- **Debug Mode**: Enabled
- **CORS**: Permissive (allows all origins)

### Production
- **Firebase Project**: `admesh-dev` (same as development for simplicity)
- **API URL**: `https://api.useadmesh.com`
- **Frontend URL**: `https://www.useadmesh.com`
- **Debug Mode**: Disabled
- **CORS**: Restricted to production domains

## Configuration Files

### Backend Configuration

The backend uses Python configuration classes located in `config/`:

- `config/base.py` - Base configuration class
- `config/development.py` - Development environment settings
- `config/production.py` - Production environment settings
- `config/config_manager.py` - Configuration manager and utilities

### Frontend Configuration

The dashboard uses TypeScript configuration in `src/config/environment.ts`:

- Automatic environment detection
- Environment-specific Firebase configs
- API endpoint configuration
- Feature flag management

## Environment Variables

### Required for All Environments

```bash
# Firebase credentials (file path or JSON string)
GOOGLE_APPLICATION_CREDENTIALS=/path/to/serviceAccountKey.json

# OpenAI API key
OPENAI_API_KEY=your_openai_api_key

# Resend API key for emails
RESEND_API_KEY=your_resend_api_key
```

### Development Environment

```bash
ENV=development
DEBUG=true
LOG_LEVEL=DEBUG

# Optional: Override default URLs
API_BASE_URL=http://localhost:8080
FRONTEND_URL=http://localhost:3000

# Optional: Use Firestore emulator
USE_FIRESTORE_EMULATOR=true
FIRESTORE_EMULATOR_HOST=localhost:8080
```

### Staging Environment

```bash
ENV=staging
DEBUG=false
LOG_LEVEL=INFO

# Firebase configuration
FIREBASE_API_KEY_STAGING=your_staging_api_key
FIREBASE_MESSAGING_SENDER_ID_STAGING=your_staging_sender_id
FIREBASE_APP_ID_STAGING=your_staging_app_id

# Stripe configuration (test mode)
STRIPE_SECRET_KEY_STAGING=sk_test_...
STRIPE_WEBHOOK_SECRET_STAGING=whsec_...

# Optional: Override default URLs
API_BASE_URL=https://api-staging.useadmesh.com
FRONTEND_URL=https://staging.useadmesh.com
```

### Production Environment

```bash
ENV=production
DEBUG=false
LOG_LEVEL=WARNING

# Firebase configuration
FIREBASE_API_KEY_PROD=your_production_api_key
FIREBASE_MESSAGING_SENDER_ID_PROD=your_production_sender_id
FIREBASE_APP_ID_PROD=your_production_app_id

# Stripe configuration (live mode)
STRIPE_SECRET_KEY_PROD=sk_live_...
STRIPE_WEBHOOK_SECRET_PROD=whsec_...

# Optional: Override default URLs
API_BASE_URL=https://api.useadmesh.com
FRONTEND_URL=https://www.useadmesh.com
```

## SDK Configuration

### Python SDK

```python
import os
from admesh import Admesh

# Environment-based configuration
os.environ['ADMESH_ENVIRONMENT'] = 'development'  # or 'staging', 'production'
os.environ['ADMESH_API_KEY'] = 'your_api_key'

# The SDK will automatically use the appropriate base URL
client = Admesh()

# Or override explicitly
client = Admesh(
    api_key='your_api_key',
    base_url='https://api-staging.useadmesh.com'  # for staging
)
```

### TypeScript SDK

```typescript
import { Admesh } from 'admesh';

// Environment-based configuration
process.env.ADMESH_ENVIRONMENT = 'development'; // or 'staging', 'production'
process.env.ADMESH_API_KEY = 'your_api_key';

// The SDK will automatically use the appropriate base URL
const client = new Admesh();

// Or override explicitly
const client = new Admesh({
  apiKey: 'your_api_key',
  baseURL: 'https://api-staging.useadmesh.com' // for staging
});
```

## Validation

Use the validation script to check your environment configuration:

```bash
cd admesh-protocol
python scripts/validate_environment.py
```

This script will:
- Validate all required environment variables
- Test Firebase connectivity
- Check API configuration
- Verify external service credentials
- Validate security settings

## Deployment

### GitHub Actions

The deployment workflows automatically set environment variables based on the target environment:

- **Staging**: Triggered by pushes to `staging` branch
- **Production**: Triggered by pushes to `main` branch

Required GitHub Secrets:
- `FIREBASE_CREDENTIALS_STAGING` / `FIREBASE_CREDENTIALS_PROD`
- `FIREBASE_API_KEY_STAGING` / `FIREBASE_API_KEY_PROD`
- `FIREBASE_MESSAGING_SENDER_ID_STAGING` / `FIREBASE_MESSAGING_SENDER_ID_PROD`
- `FIREBASE_APP_ID_STAGING` / `FIREBASE_APP_ID_PROD`
- `OPENAI_API_KEY`
- `STRIPE_SECRET_KEY_STAGING` / `STRIPE_SECRET_KEY_PROD`
- `STRIPE_WEBHOOK_SECRET_STAGING` / `STRIPE_WEBHOOK_SECRET_PROD`
- `RESEND_API_KEY`

### Manual Deployment

For manual deployments, ensure all required environment variables are set before starting the application.

## Troubleshooting

### Common Issues

1. **Firebase initialization fails**
   - Check that `GOOGLE_APPLICATION_CREDENTIALS` is set correctly
   - Verify the credentials file exists and is valid JSON
   - Ensure the Firebase project ID matches the environment

2. **API endpoints not working**
   - Verify the `ENV` environment variable is set correctly
   - Check that the API base URL is accessible
   - Ensure CORS origins include your frontend domain

3. **Missing environment variables**
   - Run the validation script to identify missing variables
   - Check that all required secrets are set in GitHub Actions
   - Verify environment-specific variables are configured

### Debug Mode

Enable debug mode to get detailed logging:

```bash
DEBUG=true
LOG_LEVEL=DEBUG
```

This will provide detailed information about:
- Configuration loading
- Firebase initialization
- API requests and responses
- Environment detection

## Best Practices

1. **Never commit credentials** to version control
2. **Use environment-specific Firebase projects** to isolate data
3. **Test configuration changes** in staging before production
4. **Validate environment setup** before deployment
5. **Monitor logs** for configuration-related errors
6. **Use the validation script** regularly to catch issues early
