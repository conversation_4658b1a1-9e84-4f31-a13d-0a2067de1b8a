#!/usr/bin/env python3
"""
Test script for environment configuration
Tests the configuration system with different environment settings
"""

import os
import sys
import tempfile
import json

# Add the parent directory to the path so we can import our config
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_development_config():
    """Test development configuration"""
    print("🧪 Testing Development Configuration")
    
    # Set environment
    os.environ['ENV'] = 'development'
    
    try:
        from config.config_manager import get_config, config_manager
        
        # Reload config to pick up environment change
        config_manager.reload_config()
        config = get_config()
        
        assert config.environment == 'development'
        assert config.debug == True
        assert config.api_base_url == 'http://localhost:8080'
        assert config.firebase_config['project_id'] == 'admesh-dev'
        assert '*' in config.cors_origins  # Development allows all origins
        
        print("✅ Development configuration test passed")
        return True
        
    except Exception as e:
        print(f"❌ Development configuration test failed: {e}")
        return False


def test_staging_config():
    """Test staging configuration"""
    print("🧪 Testing Staging Configuration")
    
    # Set environment and required variables
    os.environ['ENV'] = 'staging'
    os.environ['FIREBASE_API_KEY_STAGING'] = 'test_staging_api_key'
    os.environ['FIREBASE_MESSAGING_SENDER_ID_STAGING'] = 'test_staging_sender_id'
    os.environ['FIREBASE_APP_ID_STAGING'] = 'test_staging_app_id'
    
    try:
        from config.config_manager import get_config, config_manager
        
        # Reload config to pick up environment change
        config_manager.reload_config()
        config = get_config()
        
        assert config.environment == 'staging'
        assert config.api_base_url == 'https://api-staging.useadmesh.com'
        assert config.firebase_config['project_id'] == 'admesh-staging'
        assert 'https://staging.useadmesh.com' in config.cors_origins
        
        print("✅ Staging configuration test passed")
        return True
        
    except Exception as e:
        print(f"❌ Staging configuration test failed: {e}")
        return False


def test_production_config():
    """Test production configuration"""
    print("🧪 Testing Production Configuration")
    
    # Set environment and required variables
    os.environ['ENV'] = 'production'
    os.environ['FIREBASE_API_KEY_PROD'] = 'test_prod_api_key'
    os.environ['FIREBASE_MESSAGING_SENDER_ID_PROD'] = 'test_prod_sender_id'
    os.environ['FIREBASE_APP_ID_PROD'] = 'test_prod_app_id'
    os.environ['OPENAI_API_KEY'] = 'test_openai_key'
    os.environ['STRIPE_SECRET_KEY_PROD'] = 'test_stripe_key'
    os.environ['STRIPE_WEBHOOK_SECRET_PROD'] = 'test_webhook_secret'
    os.environ['RESEND_API_KEY'] = 'test_resend_key'
    
    try:
        from config.config_manager import get_config, config_manager
        
        # Reload config to pick up environment change
        config_manager.reload_config()
        config = get_config()
        
        assert config.environment == 'production'
        assert config.debug == False
        assert config.api_base_url == 'https://api.useadmesh.com'
        assert config.firebase_config['project_id'] == 'admesh-prod'
        assert 'https://www.useadmesh.com' in config.cors_origins
        
        print("✅ Production configuration test passed")
        return True
        
    except Exception as e:
        print(f"❌ Production configuration test failed: {e}")
        return False


def test_environment_detection():
    """Test automatic environment detection"""
    print("🧪 Testing Environment Detection")
    
    try:
        from config.base import BaseConfig
        
        # Test explicit ENV setting
        os.environ['ENV'] = 'test_env'
        assert BaseConfig.get_environment() == 'test_env'
        
        # Test Cloud Run detection
        del os.environ['ENV']
        os.environ['K_SERVICE'] = 'test_service'
        assert BaseConfig.get_environment() == 'production'
        
        # Test default
        del os.environ['K_SERVICE']
        assert BaseConfig.get_environment() == 'development'
        
        print("✅ Environment detection test passed")
        return True
        
    except Exception as e:
        print(f"❌ Environment detection test failed: {e}")
        return False


def test_firebase_credentials_path():
    """Test Firebase credentials path resolution"""
    print("🧪 Testing Firebase Credentials Path")
    
    try:
        from config.config_manager import get_config, config_manager
        
        # Test explicit credentials path
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump({"test": "credentials"}, f)
            temp_path = f.name
        
        os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = temp_path
        os.environ['ENV'] = 'development'
        
        config_manager.reload_config()
        config = get_config()
        
        assert config.get_firebase_credentials_path() == temp_path
        
        # Clean up
        os.unlink(temp_path)
        del os.environ['GOOGLE_APPLICATION_CREDENTIALS']
        
        print("✅ Firebase credentials path test passed")
        return True
        
    except Exception as e:
        print(f"❌ Firebase credentials path test failed: {e}")
        return False


def test_feature_flags():
    """Test feature flags configuration"""
    print("🧪 Testing Feature Flags")
    
    try:
        from config.config_manager import get_config, config_manager
        
        # Test development feature flags
        os.environ['ENV'] = 'development'
        config_manager.reload_config()
        config = get_config()
        
        if hasattr(config, 'feature_flags'):
            flags = config.feature_flags
            assert isinstance(flags, dict)
            assert 'enable_analytics' in flags
            
        print("✅ Feature flags test passed")
        return True
        
    except Exception as e:
        print(f"❌ Feature flags test failed: {e}")
        return False


def cleanup_environment():
    """Clean up environment variables after tests"""
    env_vars_to_clean = [
        'ENV', 'K_SERVICE', 'GOOGLE_APPLICATION_CREDENTIALS',
        'FIREBASE_API_KEY_STAGING', 'FIREBASE_MESSAGING_SENDER_ID_STAGING', 'FIREBASE_APP_ID_STAGING',
        'FIREBASE_API_KEY_PROD', 'FIREBASE_MESSAGING_SENDER_ID_PROD', 'FIREBASE_APP_ID_PROD',
        'OPENAI_API_KEY', 'STRIPE_SECRET_KEY_PROD', 'STRIPE_WEBHOOK_SECRET_PROD', 'RESEND_API_KEY'
    ]
    
    for var in env_vars_to_clean:
        if var in os.environ:
            del os.environ[var]


def main():
    """Run all configuration tests"""
    print("🚀 Running AdMesh Environment Configuration Tests")
    print("="*60)
    
    # Store original environment
    original_env = dict(os.environ)
    
    tests = [
        test_environment_detection,
        test_development_config,
        test_staging_config,
        test_production_config,
        test_firebase_credentials_path,
        test_feature_flags
    ]
    
    passed = 0
    failed = 0
    
    try:
        for test in tests:
            try:
                if test():
                    passed += 1
                else:
                    failed += 1
            except Exception as e:
                print(f"❌ Test {test.__name__} failed with exception: {e}")
                failed += 1
            
            print()  # Add spacing between tests
    
    finally:
        # Restore original environment
        cleanup_environment()
        os.environ.update(original_env)
    
    print("="*60)
    print(f"📊 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("✅ All configuration tests passed!")
        return 0
    else:
        print("❌ Some configuration tests failed!")
        return 1


if __name__ == "__main__":
    sys.exit(main())
